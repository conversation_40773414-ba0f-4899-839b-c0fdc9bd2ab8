<template>
  <view class="content" v-if="isLogined" >
    <view class="search-con-out">
      <SearchBar ref="searchbarref" :is-big="false" @getNearShop="searchShop" placeholder="请输入营业厅名称/地址" :bigFont="careClass=='care-con'" />
    </view>
    <view class="shop-container" :class="[careClass]">
      <template v-if="careClass=='care-con'">
        <ShopItemCare v-for="item in hallList" :key="item.stationCode" :item="item" :max-num-length="maxLength" />
      </template>
      <template v-else>
        <ShopItem v-for="item in hallList" :key="item.stationCode" :item="item" :max-num-length="maxLength" />
      </template>
      <uni-load-more v-if="!showEmpty" iconType="auto" :status="status" />
      <view v-if="showEmpty">
        <view class="empty-img"></view>
        <view class="empty-text" >
          暂无厅店信息
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import paiduiApi from '@/api/paidui.js'
import SearchBar from '@/components/searchbar.vue'
import ShopItem from "./components/shopitem.vue"
import ShopItemCare from "./components/shopitem-care.vue"
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
import { getLocationAndProv } from '@/utils/location.js'
import { useOptionsStore } from '@/store/options.js'
import { initRegion } from "@/utils/region.js"
export default {
  data() {
    return {
      hallList: [],
      currentPage:1,
      pageSize:10,
      finished:false,
      searchCriteria:"" ,//搜索条件
      status: 'more', // 加载状态 loading加载中 more加载前 no-more加载后 没有更多数据
      showEmpty:false,
      maxLength:4,
      careClass:null
    }
  },
  components:{
    SearchBar,
    ShopItem,
    ShopItemCare
  },
  computed: {
    ...mapState(useUserStore, ['info', 'isLogined','geo']),
    ...mapState(useOptionsStore,['verType'])
  },
  onLoad(options) {
    if(this.verType=='care'){
      this.careClass = 'care-con'
      this.maxLength = 3
    }
    const optionsStore = useOptionsStore()
    optionsStore.setVerType(options)
    my.setNavigationBar({
      reset: true,
      title: this.$t('mpappTitle'),
    });
  },
  watch:{
    isLogined: {
      handler(newVal) {
        if(newVal){
          this.handleLogin()
        }
      },
      immediate: true
    }
  },
  methods: {
    async handleLogin() {
      await getLocationAndProv({
        provinceCode:this.geo.province,
        cityCode:this.geo.city
      })
      initRegion()
      this.getHallList()
    },
    searchShop(searchCriteria){
      this.searchCriteria = searchCriteria
      this.currentPage = 1
      this.finished = false
      this.hallList=[]
      console.log('搜索条件变更', searchCriteria)
      this.getHallList()
    },
    getHallList() {
      let data = {
        "mobile": this.info.msisdn || '',
        "latitude":this.geo.latitude,
        "longitude":this.geo.longitude,
        "currentPage":this.currentPage,
        "pageSize":this.pageSize,
        'cityCode':this.geo.city
      }
      if (this.searchCriteria) data.searchCriteria = this.searchCriteria
      this.status = 'loading'
      this.showEmpty = false
      paiduiApi.queryHallList(data).then(res=> {
        this.$refs.searchbarref.handleInputBlur()
        if(res.resultData){
          if(this.currentPage === 1){
            this.hallList=[]
          }
          // res.resultData.list.forEach(item => {
          //   item.serviceInfoShort = (item.newServiceInfo || '').slice(0, this.maxLength)
          // })
          const processedList = res.resultData.list.map(item => ({
            ...item,
            serviceInfoShort: (item.newServiceInfo || '').slice(0, this.maxLength)
          }))
          this.hallList = this.hallList.concat(processedList)
          // this.hallList = this.hallList.concat(res.resultData.list)
          if(res.resultData.total>res.resultData.pageSize*res.resultData.pageNo){
            this.currentPage++
            this.status = 'more'
          }else{
            this.finished = true
            this.status = 'no-more'
            if(this.hallList.length==0){
              this.showEmpty = true
            }
          }
        }
      }).catch(error => {
        console.error('获取营业厅列表失败:', error)
        this.finished = true
        this.status = 'no-more'
        if(this.hallList.length==0){
          this.showEmpty = true
        }
        // 显示错误提示
      })
    }
  },
  // 页面处理函数--监听用户上拉触底
  onReachBottom() {
    if(!this.finished){
      this.getHallList()
    }
  }
}
</script>
<style>
.content {
  display: flex;
  background-color: #fff;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top:100px;
}
.search-con-out{
  width: 750px;
  top: 0;
  position: fixed;
  background: #fff;
  z-index:10;
}
.logo {
  height: 200px;
  width: 200px;
  margin-top: 200px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50px;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36px;
  color: #8f8f94;
}

.shop-container {
  background-color: #fff;
  min-height: calc(100vh - 96px);
}
.empty-img{
  background: url('@/static/payend/empty.jpg') no-repeat center;
  height:452px;
  background-size: contain;
  width:750px;
}
.empty-text{
  text-align: center;
}
.care-con .uni-load-more__text{
  font-size: 24px;
}
</style>
