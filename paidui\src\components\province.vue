<template>
  <uni-popup ref="popup" background-color="#fff">
    <view class="popup-container" :class="[careClass]">
      <view class="popup-title">
        <view class="title">请选择城市</view>
        <view class="close" @click="closePopup">取消</view>
      </view>
      <view class="popup-content">
        <view class="left-con con">
          <view v-for="(provinceInfo,index) in provinceList" :key="provinceInfo.provinceCode" class="scope-item" :class="{'active':provinceIndex==index}" @click="getProvince(index,provinceInfo)">
            {{ provinceInfo.provinceName }}
          </view>
        </view>
        <view class="right-con con">
          <view v-for="cityInfo in cityList" :key="cityInfo.cityCode" class="scope-item" @click="getCity(cityInfo)">
            {{ cityInfo.cityName }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>
<script>
import { useRegionStore } from '@/store/region'
import { useUserStore } from '@/store/user'
import { useOptionsStore } from '@/store/options'
import { mapState } from 'pinia'
const region = useRegionStore()
export default {
  data() {
    return {
      provinceIndex:0,
      proValue:{}
    }
  },
  props: {
    serviceInfo: {
      type: Object,
      default() { return null }
    }
  },
  computed: {
    ...mapState(useUserStore, ['geo']),
    ...mapState(useOptionsStore,['verType']),
    provinceList:function(){
      return region.info
    },
    cityList:function(){
      return this.provinceList&&this.provinceList[this.provinceIndex] ? this.provinceList[this.provinceIndex].children : []
    },
    careClass:function(){
      return this.verType=='care' ? 'care-con' : ''
    }
  },
  methods:{
    open(){
      this.$refs.popup.open('left')
      this.provinceIndex = this.provinceList.findIndex(item=>item.provinceCode==this.geo.province)
    },
    closePopup(){
      this.$refs.popup.close()
    },
    getProvince(index,provinceInfo){
      this.provinceIndex = index
      this.proValue = {
        province:provinceInfo.provinceCode
      }
    },
    getCity(cityInfo){
      this.proValue = {
        ...this.proValue,
        city:cityInfo.cityCode,
        cityName:cityInfo.cityName
      }
      this.toogle()
      this.closePopup()
    },
    toogle(){
      this.$emit('toggleCity',this.proValue)
    }
  }
}
</script>
<style lang="scss" scoped>
.popup-container{
  width:750px;
  .popup-title{
    padding:30px;
    color: rgba(0,0,0,0.4);
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title,.close{
      font-size: 28px;
    }
    .close{
      color: #007EFF;
    }
  }
  .popup-content{
    display: flex;
    .scope-item{
      height:80px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: #3D3D3D;
    }
    .con{
      width: 240px;
      height:1230px;
      overflow: scroll;
    }
    .left-con .scope-item{
      background: #F5F7FB;
      &.active{
        background: #fff;
        &::after{
          display: block;
          width: 6px;
          height: 33px;
          background: linear-gradient( 360deg, #5DADFF 0%, #007EFF 100%);
          border-radius: 90px 90px 90px 90px;
          content:'';
          position: absolute;
          left:0;
        }
      }
    }
    .right-con .scope-item{
      justify-content: left;
      padding-left: 60px;
    }
  }
}
.care-con{
  .title,.close,.scope-item{
    font-size: 36px!important;
  }
  .title,.scope-item{
    font-weight: bold;
  }
}
</style>
