<template>
  <view class="card" :class="careClass">
    <!-- 标题区域 -->
    <view v-if="!hideTitle" class="title">
      <view class="title-name">
        {{ $t('takeInfo') }}
      </view>
      <view class="title-btn" @click="handleToTickRecord">
        {{ $t('takeRecord') }}
      </view>
      <view
        :class="arrowIconClass"
        @click="handleToTickRecord"
        role="button"
        :aria-label="$t('takeRecord')"
      ></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <view class="number-box">
        <view class="number-box-title">
          {{ $t('takeNumber') }}
        </view>
        <view class="number-box-number">
          {{ displayTicketNo }}
        </view>
        <view class="number-box-wait">
          <view>
            {{ $t('waitCount') }}&nbsp;
          </view>
          <view class="wait-number">
            {{ totalWaitCount }}
          </view>
        </view>
        <view
          v-if="canCancelQueue"
          class="btn-item"
          @click.stop="handleShowTips"
          role="button"
          :aria-label="$t('cancelQueue')"
        >
          {{ $t('cancelQueue') }}
        </view>
      </view>

      <BaseInfo
        base-style="picknumber"
        :hall-detail="hallDetail"
        @get-wait-count="handleGetWaitCount"
      />

      <view class="tips-title">
        {{ $t('wxTip') }}
      </view>
      <view class="tips-content">
        {{ $t('wxTakeSecTip') }}
      </view>
    </view>

    <Tips
      ref="tipsPopup"
      @cancel-queuing="handleCancelQueuing"
    />
    <view class="card-footer"></view>
  </view>
</template>
<script>
import BaseInfo from './baseinfo.vue'
import { useOptionsStore } from '@/store/options.js'
import Tips from '@/components/tips.vue'
import { mapState } from 'pinia'
import paiduiApi from '@/api/paidui.js'
import { useUserStore } from '@/store/user'

// 常量定义
const VERSION_TYPES = {
  CARE: 'care',
  EN: 'en'
}

const CSS_CLASSES = {
  CARE: 'care-con',
  EN: 'en-con',
  ARROW_BLUE: 'icon-arrow-blue',
  ARROW_BLUE_LITTLE: 'icon-arrow-blue-little'
}

const API_SUCCESS_CODE = '0000'
const CANCEL_ENABLED_VALUE = 1

export default {
  name: 'PickupNumber',

  components: {
    BaseInfo,
    Tips
  },

  props: {
    hideTitle: {
      type: Boolean,
      default: false
    },
    hallDetail: {
      type: Object,
      default: () => ({})
      // validator: (value) => {
      //   return value && typeof value === 'object'
      // }
    }
  },

  data() {
    return {
      waitCountData: null,
      isLoading: false,
      error: null
    }
  },

  computed: {
    ...mapState(useOptionsStore, ['verType']),

    /**
     * 根据版本类型返回对应的CSS类名
     */
    careClass() {
      const typeMap = {
        [VERSION_TYPES.CARE]: CSS_CLASSES.CARE,
        [VERSION_TYPES.EN]: CSS_CLASSES.EN
      }
      return typeMap[this.verType] || ''
    },

    /**
     * 计算等待人数，确保返回数字类型
     */
    totalWaitCount() {
      const waitNum = this.hallDetail?.queueWaitNum
      return typeof waitNum === 'number' && waitNum >= 0 ? waitNum : 0
    },

    /**
     * 显示的票号，处理空值情况
     */
    displayTicketNo() {
      return this.hallDetail?.ticketNo || '--'
    },

    /**
     * 是否可以取消排队
     */
    canCancelQueue() {
      return this.hallDetail?.cancelTakeNumber === CANCEL_ENABLED_VALUE
    },

    /**
     * 箭头图标的CSS类名
     */
    arrowIconClass() {
      return this.careClass === CSS_CLASSES.CARE
        ? CSS_CLASSES.ARROW_BLUE
        : CSS_CLASSES.ARROW_BLUE_LITTLE
    }
  },

  methods: {
    /**
     * 跳转到取票记录页面
     */
    handleToTickRecord() {
      try {
        const url = '/pages/recordlist/index?basetyle=picknumber'
        uni.navigateTo({ url })
      } catch (error) {
        console.error('导航失败:', error)
        this.handleError('页面跳转失败')
      }
    },

    /**
     * 处理等待人数更新
     * @param {Object} res - 响应数据
     */
    handleGetWaitCount(res) {
      if (res && typeof res === 'object') {
        this.waitCountData = res
      }
    },

    /**
     * 显示取消排队提示
     */
    handleShowTips() {
      try {
        if (this.$refs.tipsPopup && typeof this.$refs.tipsPopup.showTips === 'function') {
          this.$refs.tipsPopup.showTips()
        }
      } catch (error) {
        console.error('显示提示失败:', error)
        this.handleError('操作失败，请重试')
      }
    },

    /**
     * 处理取消排队操作
     */
    async handleCancelQueuing() {
      if (this.isLoading) return

      try {
        this.isLoading = true
        this.error = null

        const userStore = useUserStore()
        const userInfo = userStore.info

        // 参数验证
        if (!userInfo?.msisdn) {
          throw new Error('用户信息不完整')
        }

        if (!this.hallDetail?.stationCode) {
          throw new Error('店铺信息不完整')
        }

        const requestData = {
          mobile: userInfo.msisdn, // 手机号
          stationCode: this.hallDetail.stationCode // 店铺编码
        }

        const response = await paiduiApi.cancelTakeNumber(requestData)

        if (response?.bizCode === API_SUCCESS_CODE) {
          // 关闭弹窗
          if (this.$refs.tipsPopup && typeof this.$refs.tipsPopup.closePopup === 'function') {
            this.$refs.tipsPopup.closePopup()
          }

          // 发送事件通知父组件
          this.$emit('changeTab','cancleAppointment')
        } else {
          throw new Error(response?.bizMsg || '取消排队失败')
        }
      } catch (error) {
        console.error('取消排队失败:', error)
        this.handleError(error.message || '取消排队失败，请重试')
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 统一错误处理
     * @param {string} message - 错误信息
     */
    handleError(message) {
      this.error = message
      // 可以在这里添加全局错误提示组件的调用
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/home/<USER>";
.number-box{
  width:283px;
  min-height:274px;
  margin:34px auto 0;
  text-align: center;
  padding-top:12px;
  &-title,&-wait{
    font-size: 28px;
    color:#000;
    line-height: 40px;
  }
  &-number{
    font-size: 90px;
    color: #007EFF;
    line-height: 72px;
    font-weight: bold;
    margin:24px auto 24px;
  }
  &-wait{
    display: flex;
    align-items:center;
    justify-content: center;
    margin-top:12px;
    line-height: 62px;
    .wait-number{
      font-size: 44px;
      font-weight: bold;
      margin-left:10px;
    }
  }
}
.btn-item{
  font-size: 32px;
  width: 258px;
  height: 80px;
  box-sizing: border-box;
  border-radius: 90px 90px 90px 90px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #007EFF;
  color:#007EFF;
  margin:20px auto 0;
}
.care-con{
  margin-bottom: 50px;
  .title{
    height:80px;
    .title-name{
      font-size: 36px;
      font-weight: bold;
    }
    .title-btn{
      font-size: 32px;
      font-weight: bold;
    }
    .icon-arrow1{
      font-size: 32px;
    }
  }
  .number-box{
    padding-top:2px;
    height:auto!important;
  }
  .number-box-number{
    margin:36px auto 18px!important;
    font-size: 104px!important;
    line-height: 104px!important;
  }
  .number-box-title{
    font-size: 32px!important;
    font-weight: bold;
  }
  .number-box-wait{
    font-size: 36px!important;
    font-weight: bold;
    line-height: 44px!important;
  }
  .wait-number{
    font-size: 44px!important;
  }
  &.content{
    margin-top:48px!important;
  }
  .btn-item{
    font-size: 40px;
  }
}
.en-con{
  .title-name{
    width:300px;
    font-size: 24px!important;
  }
  .title-btn{
    width: 230px;
  }
  .btn-item{
    width: 290px;
  }
}
</style>
